#!/usr/bin/env python3
"""
检查项目缺少的Python包
"""

import subprocess
import sys
import re
import tomli

def get_installed_packages():
    """获取已安装的包列表"""
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'list'], 
                              capture_output=True, text=True, check=True)
        installed = {}
        for line in result.stdout.split('\n')[2:]:  # 跳过标题行
            if line.strip():
                parts = line.split()
                if len(parts) >= 2:
                    installed[parts[0].lower()] = parts[1]
        return installed
    except subprocess.CalledProcessError as e:
        print(f"Error getting installed packages: {e}")
        return {}

def parse_requirement(req_str):
    """解析依赖字符串，提取包名"""
    # 移除条件表达式 (如 ; platform_machine == 'x86_64')
    req_str = req_str.split(';')[0].strip()
    
    # 提取包名 (移除版本约束)
    package_name = re.split(r'[><=!]', req_str)[0].strip()
    return package_name.lower()

def get_project_dependencies():
    """从 pyproject.toml 获取项目依赖"""
    try:
        with open('pyproject.toml', 'rb') as f:
            data = tomli.load(f)
            deps = data.get('project', {}).get('dependencies', [])
            return [parse_requirement(dep) for dep in deps]
    except Exception as e:
        print(f"Error reading pyproject.toml: {e}")
        return []

def check_missing_packages():
    """检查缺少的包"""
    print("🔍 检查项目依赖包...")
    
    installed = get_installed_packages()
    required = get_project_dependencies()
    
    print(f"📦 项目需要 {len(required)} 个包")
    print(f"✅ 已安装 {len(installed)} 个包")
    
    missing = []
    installed_required = []
    
    for pkg in required:
        if pkg in installed:
            installed_required.append(pkg)
        else:
            missing.append(pkg)
    
    print(f"\n✅ 已安装的必需包 ({len(installed_required)}):")
    for pkg in sorted(installed_required)[:10]:  # 只显示前10个
        print(f"  ✓ {pkg}")
    if len(installed_required) > 10:
        print(f"  ... 还有 {len(installed_required) - 10} 个包")
    
    if missing:
        print(f"\n❌ 缺少的包 ({len(missing)}):")
        for pkg in sorted(missing):
            print(f"  ✗ {pkg}")
        
        return missing
    else:
        print("\n🎉 所有必需的包都已安装！")
        return []

def generate_install_command(missing_packages):
    """生成安装命令"""
    if not missing_packages:
        return None
    
    # 从 pyproject.toml 获取完整的依赖规范
    try:
        with open('pyproject.toml', 'rb') as f:
            data = tomli.load(f)
            deps = data.get('project', {}).get('dependencies', [])
            
        # 创建包名到完整规范的映射
        dep_map = {}
        for dep in deps:
            pkg_name = parse_requirement(dep)
            dep_map[pkg_name] = dep.split(';')[0].strip()  # 移除条件表达式
        
        # 构建安装命令
        install_specs = []
        for pkg in missing_packages:
            if pkg in dep_map:
                install_specs.append(f'"{dep_map[pkg]}"')
            else:
                install_specs.append(pkg)
        
        return f"pip install {' '.join(install_specs)}"
    
    except Exception as e:
        print(f"Error generating install command: {e}")
        return f"pip install {' '.join(missing_packages)}"

if __name__ == "__main__":
    missing = check_missing_packages()
    
    if missing:
        print(f"\n📋 安装命令:")
        install_cmd = generate_install_command(missing)
        print(f"{install_cmd}")
        
        print(f"\n💡 建议分批安装以避免冲突:")
        # 将包分成小批次
        batch_size = 10
        for i in range(0, len(missing), batch_size):
            batch = missing[i:i+batch_size]
            batch_specs = []
            try:
                with open('pyproject.toml', 'rb') as f:
                    data = tomli.load(f)
                    deps = data.get('project', {}).get('dependencies', [])
                    dep_map = {}
                    for dep in deps:
                        pkg_name = parse_requirement(dep)
                        dep_map[pkg_name] = dep.split(';')[0].strip()
                
                for pkg in batch:
                    if pkg in dep_map:
                        batch_specs.append(f'"{dep_map[pkg]}"')
                    else:
                        batch_specs.append(pkg)
                        
                print(f"pip install {' '.join(batch_specs)}")
            except:
                print(f"pip install {' '.join(batch)}")
    else:
        print("\n🚀 可以开始使用项目了！")
