#!/usr/bin/env python3
"""
测试脚本：验证项目模块导入和代码跳转功能
"""

import sys
import os

# 添加项目路径到 Python 路径
sys.path.insert(0, '.')
sys.path.insert(0, './api')

def test_basic_imports():
    """测试基础模块导入"""
    print("=== 测试基础模块导入 ===")
    
    try:
        from flask_login import login_required, current_user
        print("✓ flask_login 导入成功")
    except ImportError as e:
        print(f"✗ flask_login 导入失败: {e}")
    
    try:
        from flask import Flask, Blueprint, request
        print("✓ flask 导入成功")
    except ImportError as e:
        print(f"✗ flask 导入失败: {e}")

def test_project_imports():
    """测试项目模块导入"""
    print("\n=== 测试项目模块导入 ===")
    
    try:
        from api import settings
        print("✓ api.settings 导入成功")
    except ImportError as e:
        print(f"✗ api.settings 导入失败: {e}")
    
    try:
        from api.db.services.user_service import UserService
        print("✓ UserService 导入成功")
    except ImportError as e:
        print(f"✗ UserService 导入失败: {e}")
    
    try:
        from api.utils.api_utils import get_json_result
        print("✓ get_json_result 导入成功")
    except ImportError as e:
        print(f"✗ get_json_result 导入失败: {e}")

def test_code_navigation():
    """测试代码导航功能"""
    print("\n=== 代码导航测试提示 ===")
    print("请在 VS Code 中测试以下功能：")
    print("1. 打开 api/apps/user_app.py")
    print("2. 将鼠标悬停在 'UserService' 上")
    print("3. 按住 Ctrl 并点击 'UserService'")
    print("4. 应该能够跳转到 api/db/services/user_service.py")
    print("5. 右键点击 'get_json_result' 选择 'Go to Definition'")
    print("6. 应该能够跳转到 api/utils/api_utils.py")

if __name__ == "__main__":
    test_basic_imports()
    test_project_imports()
    test_code_navigation()
    print("\n=== 测试完成 ===")
