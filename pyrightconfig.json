{"include": ["api", "rag", "deepdoc", "agent", "graphrag", "agentic_reasoning", "plugin"], "exclude": ["**/node_modules", "**/__pycache__", ".venv", "venv"], "extraPaths": ["./api", "./rag", "./deepdoc", "./agent", "./graphrag", "./agentic_reasoning", "./plugin"], "pythonVersion": "3.10", "pythonPlatform": "Linux", "typeCheckingMode": "basic", "useLibraryCodeForTypes": true, "autoSearchPaths": true, "autoImportCompletions": true, "indexing": true}